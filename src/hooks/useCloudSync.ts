/**
 * Cloud Sync Hook
 * 
 * Manages Google Drive cloud synchronization functionality including
 * authentication, file operations, and sync status management.
 */

import { useCallback, useState } from 'react';
import { useLocalStorage } from './useLocalStorage';
import { useDataBackup } from './useDataBackup';
import { useAsyncError } from './useAsyncError';
import { invoke } from '@tauri-apps/api/core';
import {
  CloudSyncConfig,
  CloudSyncStatus,
  SyncResult,
  GoogleDriveAuthResult,
  GoogleDriveFileMetadata,
  SyncConflict,
  // AuthStatus, // Commented out unused import
  // SyncStatus, // Commented out unused import
  // ConflictResolution, // Commented out unused import
  DEFAULT_CLOUD_SYNC_CONFIG,
  isSyncDue,
  getNextSyncTime,
  formatSyncStatus,
} from '../types/cloudSync';

// Google OAuth configuration
// In Tauri apps, use import.meta.env instead of process.env
// Handle Jest environment where import.meta might not be available
const getEnvVar = (key: string, defaultValue: string = '') => {
  // Check if we're in a test environment (Jest)
  if (typeof process !== 'undefined' && process.env.NODE_ENV === 'test') {
    return defaultValue;
  }

  // Check if import.meta is available (Vite environment)
  try {
    // Use eval to avoid TypeScript compilation issues with import.meta
    const importMeta = eval('import.meta');
    if (importMeta && importMeta.env) {
      return importMeta.env[key] || defaultValue;
    }
  } catch {
    // Fallback if import.meta is not available
  }

  return defaultValue;
};

const GOOGLE_CLIENT_ID = getEnvVar('VITE_GOOGLE_CLIENT_ID');
const GOOGLE_CLIENT_SECRET = getEnvVar('VITE_GOOGLE_CLIENT_SECRET');
const REDIRECT_URI = 'http://localhost:3000/auth/callback';

// App-specific file configuration
// const APP_FOLDER_NAME = 'TimeTrackerAppData'; // Commented out unused variable
const BACKUP_FILE_NAME = 'time_tracker_backup.json';

export interface UseCloudSyncOptions {
  onSyncSuccess?: (result: SyncResult) => void;
  onSyncError?: (error: string) => void;
  onAuthSuccess?: () => void;
  onAuthError?: (error: string) => void;
}

/**
 * Hook for managing Google Drive cloud synchronization
 */
export function useCloudSync(options: UseCloudSyncOptions = {}) {
  const [config, setConfig] = useLocalStorage<CloudSyncConfig>('cloudSyncConfig', DEFAULT_CLOUD_SYNC_CONFIG);
  const [status, setStatus] = useState<CloudSyncStatus>({
    authStatus: 'disconnected',
    syncStatus: 'idle',
    isAutoSyncEnabled: config.autoSync,
    remoteFileExists: false,
  });

  const { executeAsync } = useAsyncError();
  const { createBackupData, importData } = useDataBackup();

  // Update config
  const updateConfig = useCallback((updates: Partial<CloudSyncConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  }, [setConfig]);

  // Generate Google Drive authentication URL
  const getAuthUrl = useCallback(async (): Promise<string> => {
    return executeAsync(
      async () => {
        const authUrl = await invoke<string>('google_drive_get_auth_url', {
          clientId: GOOGLE_CLIENT_ID,
          redirectUri: REDIRECT_URI,
        });
        return authUrl;
      },
      {
        operation: 'get-auth-url',
        errorHandler: (error) => {
          if (options.onAuthError) {
            options.onAuthError(error.message);
          }
        },
      }
    );
  }, [executeAsync, options]);

  // Authenticate with Google Drive
  const authenticate = useCallback(async (authCode: string): Promise<boolean> => {
    setStatus(prev => ({ ...prev, authStatus: 'connecting' }));

    return executeAsync(
      async () => {
        const result = await invoke<GoogleDriveAuthResult>('google_drive_authenticate', {
          clientId: GOOGLE_CLIENT_ID,
          clientSecret: GOOGLE_CLIENT_SECRET,
          redirectUri: REDIRECT_URI,
          authCode,
        });

        if (result.success) {
          setStatus(prev => ({
            ...prev,
            authStatus: 'connected',
          }));

          if (options.onAuthSuccess) {
            options.onAuthSuccess();
          }

          return true;
        } else {
          setStatus(prev => ({
            ...prev,
            authStatus: 'error',
          }));

          if (options.onAuthError) {
            options.onAuthError(result.error || 'Authentication failed');
          }

          return false;
        }
      },
      {
        operation: 'authenticate',
        errorHandler: (error) => {
          setStatus(prev => ({
            ...prev,
            authStatus: 'error',
          }));

          if (options.onAuthError) {
            options.onAuthError(error.message);
          }
        },
      }
    );
  }, [executeAsync, options]);

  // Disconnect from Google Drive
  const disconnect = useCallback(() => {
    setStatus(prev => ({
      ...prev,
      authStatus: 'disconnected',
      syncStatus: 'idle',
      lastSyncTime: undefined,
      lastSyncSuccess: undefined,
      lastSyncError: undefined,
      remoteFileExists: false,
    }));

    updateConfig({
      enabled: false,
      autoSync: false,
      lastSyncTime: undefined,
      googleDriveFileId: undefined,
    });
  }, [updateConfig]);

  // Find or create app backup file on Google Drive
  const findOrCreateBackupFile = useCallback(async (): Promise<string> => {
    return executeAsync(
      async () => {
        // First, try to find existing backup file
        const query = `name='${BACKUP_FILE_NAME}' and mimeType='application/json'`;
        const files = await invoke<GoogleDriveFileMetadata[]>('google_drive_list_files', {
          query,
        });

        if (files.length > 0) {
          // Use existing file
          const fileId = files[0].id;
          updateConfig({ googleDriveFileId: fileId });
          return fileId;
        }

        // Create new backup file
        const backupData = createBackupData();
        const backupJson = JSON.stringify(backupData, null, 2);

        const fileMetadata = await invoke<GoogleDriveFileMetadata>('google_drive_upload_file', {
          filename: BACKUP_FILE_NAME,
          content: backupJson,
          parentFolderId: null, // Root folder
        });

        updateConfig({ googleDriveFileId: fileMetadata.id });
        return fileMetadata.id;
      },
      {
        operation: 'find-or-create-backup-file',
      }
    );
  }, [executeAsync, createBackupData, updateConfig]);

  // Upload data to Google Drive
  const uploadData = useCallback(async (): Promise<SyncResult> => {
    setStatus(prev => ({ ...prev, syncStatus: 'syncing' }));

    return executeAsync(
      async () => {
        const backupData = createBackupData();
        const backupJson = JSON.stringify(backupData, null, 2);

        let fileId = config.googleDriveFileId;
        let operation: 'upload' | 'download' | 'conflict' = 'upload';

        if (fileId) {
          // Update existing file
          await invoke<GoogleDriveFileMetadata>('google_drive_update_file', {
            fileId,
            content: backupJson,
          });
        } else {
          // Create new file
          fileId = await findOrCreateBackupFile();
        }

        const now = new Date().toISOString();
        updateConfig({ lastSyncTime: now });

        const result: SyncResult = {
          success: true,
          operation,
          timestamp: now,
          dataChanged: true,
        };

        setStatus(prev => ({
          ...prev,
          syncStatus: 'success',
          lastSyncTime: now,
          lastSyncSuccess: true,
          lastSyncError: undefined,
          nextScheduledSync: config.autoSync 
            ? getNextSyncTime(config, now)?.toISOString()
            : undefined,
        }));

        if (options.onSyncSuccess) {
          options.onSyncSuccess(result);
        }

        return result;
      },
      {
        operation: 'upload-data',
        errorHandler: (error) => {
          const result: SyncResult = {
            success: false,
            operation: 'upload',
            timestamp: new Date().toISOString(),
            error: error.message,
            dataChanged: false,
          };

          setStatus(prev => ({
            ...prev,
            syncStatus: 'error',
            lastSyncSuccess: false,
            lastSyncError: error.message,
          }));

          if (options.onSyncError) {
            options.onSyncError(error.message);
          }

          return result;
        },
      }
    ).finally(() => {
      setTimeout(() => {
        setStatus(prev => ({ ...prev, syncStatus: 'idle' }));
      }, 2000);
    });
  }, [executeAsync, createBackupData, config, updateConfig, findOrCreateBackupFile, options]);

  // Download data from Google Drive
  const downloadData = useCallback(async (): Promise<SyncResult> => {
    setStatus(prev => ({ ...prev, syncStatus: 'syncing' }));

    return executeAsync(
      async () => {
        const fileId = config.googleDriveFileId;
        if (!fileId) {
          throw new Error('No backup file found on Google Drive');
        }

        const backupJson = await invoke<string>('google_drive_download_file', {
          fileId,
        });

        const backupData = JSON.parse(backupJson);
        await importData(backupData);

        const now = new Date().toISOString();
        updateConfig({ lastSyncTime: now });

        const result: SyncResult = {
          success: true,
          operation: 'download',
          timestamp: now,
          dataChanged: true,
        };

        setStatus(prev => ({
          ...prev,
          syncStatus: 'success',
          lastSyncTime: now,
          lastSyncSuccess: true,
          lastSyncError: undefined,
          nextScheduledSync: config.autoSync 
            ? getNextSyncTime(config, now)?.toISOString()
            : undefined,
        }));

        if (options.onSyncSuccess) {
          options.onSyncSuccess(result);
        }

        return result;
      },
      {
        operation: 'download-data',
        errorHandler: (error) => {
          const result: SyncResult = {
            success: false,
            operation: 'download',
            timestamp: new Date().toISOString(),
            error: error.message,
            dataChanged: false,
          };

          setStatus(prev => ({
            ...prev,
            syncStatus: 'error',
            lastSyncSuccess: false,
            lastSyncError: error.message,
          }));

          if (options.onSyncError) {
            options.onSyncError(error.message);
          }

          return result;
        },
      }
    ).finally(() => {
      setTimeout(() => {
        setStatus(prev => ({ ...prev, syncStatus: 'idle' }));
      }, 2000);
    });
  }, [executeAsync, config, updateConfig, importData, options]);

  // Check for sync conflicts
  const checkForConflicts = useCallback(async (): Promise<SyncConflict | null> => {
    return executeAsync(
      async () => {
        const fileId = config.googleDriveFileId;
        if (!fileId) {
          return null;
        }

        const fileMetadata = await invoke<GoogleDriveFileMetadata>('google_drive_get_file_metadata', {
          fileId,
        });

        const remoteModified = new Date(fileMetadata.modifiedTime);
        const localModified = config.lastSyncTime ? new Date(config.lastSyncTime) : new Date(0);

        const hasRemoteChanges = remoteModified > localModified;
        const hasLocalChanges = true; // Assume local changes for now

        if (hasRemoteChanges && hasLocalChanges) {
          return {
            localModified: localModified.toISOString(),
            remoteModified: remoteModified.toISOString(),
            hasLocalChanges,
            hasRemoteChanges,
          };
        }

        return null;
      },
      {
        operation: 'check-conflicts',
      }
    );
  }, [executeAsync, config]);

  // Perform sync operation
  const sync = useCallback(async (forceDirection?: 'upload' | 'download'): Promise<SyncResult> => {
    if (status.authStatus !== 'connected') {
      throw new Error('Not authenticated with Google Drive');
    }

    if (forceDirection === 'upload') {
      return uploadData();
    }

    if (forceDirection === 'download') {
      return downloadData();
    }

    // Check for conflicts
    const conflict = await checkForConflicts();
    if (conflict) {
      // Handle conflict based on configuration
      switch (config.conflictResolution) {
        case 'local':
          return uploadData();
        case 'remote':
          return downloadData();
        case 'prompt':
          // For now, default to upload
          // In a real implementation, you'd show a dialog
          return uploadData();
        default:
          return uploadData();
      }
    }

    // No conflicts, upload local data
    return uploadData();
  }, [status.authStatus, config.conflictResolution, uploadData, downloadData, checkForConflicts]);

  // Check if sync is due
  const checkSyncDue = useCallback((): boolean => {
    return isSyncDue(config, config.lastSyncTime);
  }, [config]);

  // Perform automatic sync if due
  const performAutomaticSyncIfDue = useCallback(async (): Promise<SyncResult | null> => {
    if (!config.enabled || !config.autoSync || status.authStatus !== 'connected') {
      return null;
    }

    if (!checkSyncDue()) {
      return null;
    }

    return sync();
  }, [config.enabled, config.autoSync, status.authStatus, checkSyncDue, sync]);

  // Get formatted status
  const getFormattedStatus = useCallback((): string => {
    return formatSyncStatus(status);
  }, [status]);

  return {
    // State
    config,
    status,

    // Configuration
    updateConfig,

    // Authentication
    getAuthUrl,
    authenticate,
    disconnect,

    // Sync operations
    sync,
    uploadData,
    downloadData,
    checkForConflicts,

    // Automatic sync
    checkSyncDue,
    performAutomaticSyncIfDue,

    // Utilities
    getFormattedStatus,
  };
}
