/**
 * Mock for @tauri-apps/api/path
 */

export const appDir = jest.fn().mockResolvedValue('/mock/app');
export const appConfigDir = jest.fn().mockResolvedValue('/mock/app/config');
export const appDataDir = jest.fn().mockResolvedValue('/mock/app/data');
export const appLocalDataDir = jest.fn().mockResolvedValue('/mock/app/local-data');
export const appCacheDir = jest.fn().mockResolvedValue('/mock/app/cache');
export const appLogDir = jest.fn().mockResolvedValue('/mock/app/logs');
export const audioDir = jest.fn().mockResolvedValue('/mock/audio');
export const cacheDir = jest.fn().mockResolvedValue('/mock/cache');
export const configDir = jest.fn().mockResolvedValue('/mock/config');
export const dataDir = jest.fn().mockResolvedValue('/mock/data');
export const desktopDir = jest.fn().mockResolvedValue('/mock/desktop');
export const documentDir = jest.fn().mockResolvedValue('/mock/documents');
export const downloadDir = jest.fn().mockResolvedValue('/mock/downloads');
export const executableDir = jest.fn().mockResolvedValue('/mock/executable');
export const fontDir = jest.fn().mockResolvedValue('/mock/fonts');
export const homeDir = jest.fn().mockResolvedValue('/mock/home');
export const localDataDir = jest.fn().mockResolvedValue('/mock/local-data');
export const pictureDir = jest.fn().mockResolvedValue('/mock/pictures');
export const publicDir = jest.fn().mockResolvedValue('/mock/public');
export const resourceDir = jest.fn().mockResolvedValue('/mock/resources');
export const runtimeDir = jest.fn().mockResolvedValue('/mock/runtime');
export const templateDir = jest.fn().mockResolvedValue('/mock/templates');
export const videoDir = jest.fn().mockResolvedValue('/mock/videos');
export const logDir = jest.fn().mockResolvedValue('/mock/logs');
